FROM python:3.8-slim

RUN apt-get update && apt-get install -y \
    libx11-6 \
    libxrender1 \
    libxtst6 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    python3-tk \
    tk-dev \
    libegl1 \
    libosmesa6 \
    libosmesa6-dev \
    libgles2-mesa-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

ENV OPENBLAS_NUM_THREADS=1 \
    NUMEXPR_NUM_THREADS=1 \
    MKL_NUM_THREADS=1 \
    OMP_NUM_THREADS=1 \
    VECLIB_MAXIMUM_THREADS=1 \
    NVIDIA_VISIBLE_DEVICES=all

COPY requirements.txt ./

RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN pip install --no-cache-dir dist/*.whl

CMD ["python", "examples/MultiView.py", "--server", "--host", "0.0.0.0", "--port", "8080"]
# CMD ["tail", "-f", "/dev/null"]

